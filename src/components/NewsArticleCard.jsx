import React from 'react';
import { Link } from 'react-router-dom';

const NewsArticleCard = ({ article }) => {
  const publishedDate = new Date(article.created_at).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Create a short summary from the content
  const summary = article.content.substring(0, 150) + '...';

  return (
    <div className="news-article-card">
      <h3>{article.title}</h3>
      <p className="news-article-meta">Published on {publishedDate}</p>
      <p className="news-article-summary">{summary}</p>
      <Link to={`/news/${article.id}`} className="read-more-link">Read More</Link>
    </div>
  );
};

export default NewsArticleCard;
