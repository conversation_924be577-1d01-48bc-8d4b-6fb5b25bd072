import React from 'react';

const BoardMemberCard = ({ member }) => {
  // Use a placeholder if no image is provided
  const imageUrl = member.image_url || `https://api.dicebear.com/8.x/initials/svg?seed=${member.name}`;

  return (
    <div className="board-member-card">
      <img src={imageUrl} alt={member.name} className="board-member-image" />
      <div className="board-member-info">
        <h4>{member.name}</h4>
        <p className="board-member-position">{member.position}</p>
      </div>
    </div>
  );
};

export default BoardMemberCard;