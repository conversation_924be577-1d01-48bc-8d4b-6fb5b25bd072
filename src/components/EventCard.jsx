import React from 'react';

const EventCard = ({ event, user, onRsvp, isRsvpd, rsvpLoading }) => {
  const eventDate = new Date(event.date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
  });

  return (
    <div className="event-card">
      {event.image_url && <img src={event.image_url} alt={event.title} className="event-card-image" />}
      <div className="event-card-content">
        <h3>{event.title}</h3>
        <p className="event-card-date">{eventDate}</p>
        <p className="event-card-location"><strong>Location:</strong> {event.location}</p>
        <p>{event.description}</p>
        {user && new Date(event.date) > new Date() && (
          <button 
            onClick={() => onRsvp(event.id)} 
            className={`rsvp-btn ${isRsvpd ? 'disabled' : ''}`}
            disabled={isRsvpd || rsvpLoading}
          >
            {isRsvpd ? '✓ RSVP\'d' : 'RSVP'}
          </button>
        )}
      </div>
    </div>
  );
};

export default EventCard;