import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const AdminRoute = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div>Loading authentication status...</div>;
  }

  // 1. If no user, redirect to login
  if (!user) {
    return <Navigate to="/login" />;
  }

  // 2. If user is not an admin, redirect to the homepage
  if (user.role !== 'admin') {
    return <Navigate to="/" />;
  }

  // 3. If user is an admin, render the child routes
  return <Outlet />;
};

export default AdminRoute;
