import { Link } from 'react-router-dom';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  return (
    <footer className="flex justify-center">
      <div className="flex max-w-[960px] flex-1 flex-col">
        <footer className="flex flex-col gap-6 px-5 py-10 text-center">
          <div className="flex flex-wrap items-center justify-center gap-6 sm:flex-row sm:justify-around">
            <Link className="text-[#60758a] text-base font-normal leading-normal min-w-40 hover:text-[#111418]" to="/about">
              Privacy Policy
            </Link>
            <Link className="text-[#60758a] text-base font-normal leading-normal min-w-40 hover:text-[#111418]" to="/about">
              Terms of Service
            </Link>
            <Link className="text-[#60758a] text-base font-normal leading-normal min-w-40 hover:text-[#111418]" to="/contact">
              Contact Us
            </Link>
          </div>
          <p className="text-[#60758a] text-base font-normal leading-normal">
            © {currentYear} Sudanese Association. All rights reserved.
          </p>
        </footer>
      </div>
    </footer>
  );
};

export default Footer;
