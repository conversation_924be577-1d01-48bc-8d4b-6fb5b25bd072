import React from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Navbar = () => {
  const { user, signOut } = useAuth();

  return (
    <nav className="navbar">
      <Link to="/" className="nav-brand">SD-Asso</Link>
      <ul className="nav-links">
        <li><NavLink to="/">Home</NavLink></li>
        <li><NavLink to="/about">About</NavLink></li>
        <li><NavLink to="/events">Events</NavLink></li>
        <li><NavLink to="/news">News</NavLink></li>
        <li><NavLink to="/gallery">Gallery</NavLink></li>
        <li><NavLink to="/resources">Resources</NavLink></li>
        {user ? (
          <>
            <li><NavLink to="/members">Members Area</NavLink></li>
            {user.role === 'admin' && (
              <li><NavLink to="/admin">Admin</NavLink></li>
            )}
            <li>
              <button onClick={signOut} className="nav-logout-btn">Logout</button>
            </li>
          </>
        ) : (
          <li><NavLink to="/login">Login</NavLink></li>
        )}
      </ul>
    </nav>
  );
};

export default Navbar;