import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { supabase } from '../api/supabase';

const SingleNewsArticle = () => {
  const { id } = useParams();
  const [article, setArticle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchArticle = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('news')
          .select('*')
          .eq('id', id)
          .single(); // .single() is crucial to get one record, not an array

        if (error) throw error;
        setArticle(data);
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchArticle();
    }
  }, [id]);

  if (loading) return <div className="page-container">Loading article...</div>;
  if (error) return <div className="page-container error-message">Error: {error}</div>;
  if (!article) return <div className="page-container">Article not found.</div>;

  const publishedDate = new Date(article.created_at).toLocaleDateString('en-US', {
    year: 'numeric', month: 'long', day: 'numeric'
  });

  return (
    <div className="page-container single-article-container">
      <h1>{article.title}</h1>
      <p className="article-meta">Published on {publishedDate}</p>
      <div className="article-content" dangerouslySetInnerHTML={{ __html: article.content.replace(/\n/g, '<br />') }} />
    </div>
  );
};

export default SingleNewsArticle;