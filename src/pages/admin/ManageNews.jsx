import React, { useState, useEffect } from 'react';
import { supabase } from '../../api/supabase';

const ManageNews = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNews();
  }, []);

  const fetchNews = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from('news')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching news:', error);
    } else {
      setArticles(data);
    }
    setLoading(false);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this article?')) {
      const { error } = await supabase.from('news').delete().eq('id', id);
      if (error) {
        alert('Error deleting article: ' + error.message);
      } else {
        fetchNews(); // Refresh the list
      }
    }
  };

  if (loading) return <div className="admin-container">Loading news articles...</div>;

  return (
    <div className="admin-container">
      <h2>Manage News Articles</h2>
      <p>This is where you can add, edit, and delete news articles.</p>
      <p><em>(Full form for Add/Edit is a planned feature.)</em></p>

      <table className="admin-table">
        <thead>
          <tr>
            <th>Title</th>
            <th>Created At</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {articles.map(article => (
            <tr key={article.id}>
              <td>{article.title}</td>
              <td>{new Date(article.created_at).toLocaleDateString()}</td>
              <td>
                <button className="admin-btn-edit" disabled>Edit</button>
                <button className="admin-btn-delete" onClick={() => handleDelete(article.id)}>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ManageNews;