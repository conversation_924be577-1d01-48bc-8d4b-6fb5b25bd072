import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const AdminDashboard = () => {
  const { user } = useAuth();

  return (
    <div className="admin-container">
      <h1>Admin Dashboard</h1>
      <p>Welcome, Admin {user?.email}.</p>
      <div className="admin-nav">
        <Link to="/admin/events" className="admin-nav-link">Manage Events</Link>
        <Link to="/admin/news" className="admin-nav-link">Manage News</Link>
        <Link to="/admin/gallery" className="admin-nav-link">Manage Gallery</Link>
      </div>
    </div>
  );
};

export default AdminDashboard;