import React, { useState, useEffect } from 'react';
import { supabase } from '../../api/supabase';

const ManageGallery = () => {
  const [images, setImages] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');

  const bucketName = 'gallery';
  const folderName = 'public';

  useEffect(() => {
    fetchImages();
  }, []);

  const fetchImages = async () => {
    const { data, error } = await supabase.storage.from(bucketName).list(folderName);
    if (data) {
      const imageUrls = data
        .filter(file => file.name !== '.emptyFolderPlaceholder')
        .map(file => {
          const { data: { publicUrl } } = supabase.storage.from(bucketName).getPublicUrl(`${folderName}/${file.name}`);
          return { name: file.name, url: publicUrl };
        });
      setImages(imageUrls);
    }
  };

  const handleUpload = async (event) => {
    try {
      setUploading(true);
      setError('');
      const file = event.target.files[0];
      if (!file) return;

      const fileName = `${Date.now()}_${file.name}`;
      const { error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(`${folderName}/${fileName}`, file);

      if (uploadError) throw uploadError;
      fetchImages(); // Refresh gallery
    } catch (error) {
      setError('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (imageName) => {
    if (window.confirm(`Are you sure you want to delete ${imageName}?`)) {
      const { error } = await supabase.storage.from(bucketName).remove([`${folderName}/${imageName}`]);
      if (error) {
        alert('Error deleting image: ' + error.message);
      } else {
        fetchImages(); // Refresh gallery
      }
    }
  };

  return (
    <div className="admin-container">
      <h2>Manage Gallery</h2>
      <div className="upload-section">
        <h3>Upload New Image</h3>
        <input type="file" accept="image/*" onChange={handleUpload} disabled={uploading} />
        {uploading && <p>Uploading...</p>}
        {error && <p className="error-message">{error}</p>}
      </div>
      <div className="gallery-management-grid">
        {images.map(image => (
          <div key={image.name} className="gallery-management-item">
            <img src={image.url} alt={image.name} />
            <button onClick={() => handleDelete(image.name)} className="admin-btn-delete">Delete</button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ManageGallery;