import React, { useState, useEffect } from 'react';
import { supabase } from '../../api/supabase';

const ManageEvents = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  // Add state for form, editing, etc.

  useEffect(() => {
    fetchEvents();
  }, []);

  const fetchEvents = async () => {
    setLoading(true);
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .order('date', { ascending: false });
    
    if (error) {
      console.error('Error fetching events:', error);
    } else {
      setEvents(data);
    }
    setLoading(false);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      const { error } = await supabase.from('events').delete().eq('id', id);
      if (error) {
        alert('Error deleting event: ' + error.message);
      } else {
        fetchEvents(); // Refresh the list
      }
    }
  };

  if (loading) return <div className="admin-container">Loading events...</div>;

  return (
    <div className="admin-container">
      <h2>Manage Events</h2>
      <p>This is where you can add, edit, and delete events.</p>
      <p><em>(Full form for Add/Edit is a planned feature.)</em></p>
      
      <table className="admin-table">
        <thead>
          <tr>
            <th>Title</th>
            <th>Date</th>
            <th>Location</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {events.map(event => (
            <tr key={event.id}>
              <td>{event.title}</td>
              <td>{new Date(event.date).toLocaleString()}</td>
              <td>{event.location}</td>
              <td>
                <button className="admin-btn-edit" disabled>Edit</button>
                <button className="admin-btn-delete" onClick={() => handleDelete(event.id)}>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ManageEvents;