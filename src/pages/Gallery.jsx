import React, { useState, useEffect } from 'react';
import { supabase } from '../api/supabase';

const Gallery = () => {
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    const fetchImages = async () => {
      // Assumes a public bucket named 'gallery' and images in a 'public' folder
      const bucketName = 'gallery';
      const folderName = 'public';

      try {
        setLoading(true);
        const { data: fileList, error: listError } = await supabase.storage
          .from(bucketName)
          .list(folderName, {
            limit: 100, // Adjust as needed
            offset: 0,
            sortBy: { column: 'name', order: 'asc' },
          });

        if (listError) throw listError;

        const imageUrls = fileList
          .filter(file => file.name !== '.emptyFolderPlaceholder') // Supabase adds this file
          .map(file => {
            const { data: { publicUrl } } = supabase.storage.from(bucketName).getPublicUrl(`${folderName}/${file.name}`);
            return { name: file.name, url: publicUrl };
          });

        setImages(imageUrls);
      } catch (err) {
        setError('Could not fetch images from storage. Please ensure the bucket is public and files exist.');
        console.error('Gallery fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchImages();
  }, []);

  if (loading) return <div className="page-container">Loading gallery...</div>;
  if (error) return <div className="page-container error-message">{error}</div>;

  return (
    <div className="page-container">
      <h1>Photo Gallery</h1>
      {selectedImage && (
        <div className="lightbox-overlay" onClick={() => setSelectedImage(null)}>
          <div className="lightbox-content">
            <button className="lightbox-close" onClick={() => setSelectedImage(null)}>&times;</button>
            <img src={selectedImage} alt="Enlarged view" />
          </div>
        </div>
      )}
      {images.length > 0 ? (
        <div className="gallery-grid">
          {images.map((image) => (
            <div key={image.name} className="gallery-item" onClick={() => setSelectedImage(image.url)}>
              <img src={image.url} alt={image.name} />
            </div>
          ))}
        </div>
      ) : (
        <p>No images found in the gallery.</p>
      )}
    </div>
  );
};

export default Gallery;