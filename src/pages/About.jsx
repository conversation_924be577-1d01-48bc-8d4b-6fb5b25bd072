import React, { useState, useEffect } from 'react';
import { supabase } from '../api/supabase';
import BoardMemberCard from '../components/BoardMemberCard';

const About = () => {
  const [boardMembers, setBoardMembers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBoardMembers = async () => {
      try {
        const { data, error } = await supabase
          .from('board_members')
          .select('*')
          .order('id', { ascending: true });
        
        if (error) throw error;
        setBoardMembers(data);
      } catch (error) {
        console.error('Error fetching board members:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBoardMembers();
  }, []);

  return (
    <div className="page-container">
      <h1>About Us</h1>
      <section className="about-section">
        <h2>Our History</h2>
        <p>Founded in [Year], our association has been dedicated to supporting the Sudanese diaspora community. We have grown from a small group of individuals to a thriving organization with hundreds of members.</p>
      </section>
      <section className="about-section">
        <h2>Mission & Vision</h2>
        <p><strong>Mission:</strong> To foster a sense of community, provide support, and preserve our rich cultural heritage among Sudanese people in the region.</p>
        <p><strong>Vision:</strong> A united and empowered Sudanese community that contributes positively to the broader society while maintaining strong cultural roots.</p>
      </section>
      <section className="about-section">
        <h2>Executive Board</h2>
        {loading ? <p>Loading board members...</p> : (
          <div className="board-member-grid">
            {boardMembers.map(member => <BoardMemberCard key={member.id} member={member} />)}
          </div>
        )}
      </section>
    </div>
  );
};

export default About;