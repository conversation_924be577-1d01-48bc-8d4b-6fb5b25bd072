import React, { useState, useEffect } from 'react';
import { supabase } from '../api/supabase';
import EventCard from '../components/EventCard';
import { useAuth } from '../contexts/AuthContext';

const Events = () => {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('upcoming'); // 'all', 'upcoming', 'past'
  const [rsvps, setRsvps] = useState(new Set());
  const [rsvpLoading, setRsvpLoading] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        let query = supabase
          .from('events') // Assumes your table is named 'events'
          .select('*')
          .order('date', { ascending: true });

        const today = new Date().toISOString();
        if (filter === 'upcoming') {
          query = query.gte('date', today);
        } else if (filter === 'past') {
          query = query.lt('date', today).order('date', { ascending: false });
        }

        const { data, error } = await query;

        if (error) throw error;
        setEvents(data);
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [filter]);

  useEffect(() => {
    if (!user) return;

    const fetchRsvps = async () => {
      const { data, error } = await supabase
        .from('rsvps')
        .select('event_id')
        .eq('user_id', user.id);
      
      if (error) {
        console.error('Error fetching RSVPs:', error);
        return;
      }

      const rsvpSet = new Set(data.map(r => r.event_id));
      setRsvps(rsvpSet);
    };

    fetchRsvps();
  }, [user]);

  const handleRsvp = async (eventId) => {
    if (!user) {
      alert('You must be logged in to RSVP.');
      return;
    }
    setRsvpLoading(true);
    try {
      const { error } = await supabase
        .from('rsvps')
        .insert({ event_id: eventId, user_id: user.id });
      
      if (error) throw error;

      setRsvps(prevRsvps => new Set(prevRsvps).add(eventId));
    } catch (error) {
      alert('Failed to RSVP. You may have already registered for this event.');
      console.error('RSVP error:', error);
    } finally {
      setRsvpLoading(false);
    }
  };

  if (loading) return <div className="page-container">Loading events...</div>;
  if (error) return <div className="page-container error-message">Error: {error}</div>;

  return (
    <div className="page-container">
      <h1>Upcoming Events</h1>
      <div className="event-filters">
        <button onClick={() => setFilter('upcoming')} className={filter === 'upcoming' ? 'active' : ''}>Upcoming</button>
        <button onClick={() => setFilter('past')} className={filter === 'past' ? 'active' : ''}>Past</button>
        <button onClick={() => setFilter('all')} className={filter === 'all' ? 'active' : ''}>All</button>
      </div>
      {events.length > 0 ? (
        <div className="events-list">
          {events.map((event) => (
            <EventCard 
              key={event.id} 
              event={event} 
              user={user}
              onRsvp={handleRsvp}
              isRsvpd={rsvps.has(event.id)}
              rsvpLoading={rsvpLoading}
            />
          ))}
        </div>
      ) : (
        <p>No upcoming events at the moment. Please check back later!</p>
      )}
    </div>
  );
};

export default Events;