import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../api/supabase';

const Home = () => {
  const { user } = useAuth();
  const [latestEvents, setLatestEvents] = useState([]);
  const [latestNews, setLatestNews] = useState([]);

  useEffect(() => {
    const fetchHomepageData = async () => {
      // Fetch latest 3 upcoming events
      const { data: eventsData } = await supabase
        .from('events')
        .select('*')
        .gte('date', new Date().toISOString())
        .order('date', { ascending: true })
        .limit(3);
      setLatestEvents(eventsData || []);

      // Fetch latest 3 news articles
      const { data: newsData } = await supabase.from('news').select('*').order('created_at', { ascending: false }).limit(3);
      setLatestNews(newsData || []);
    };

    fetchHomepageData();
  }, []);

  return (
    <div className="px-40 flex flex-1 justify-center py-5">
      <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
        {/* Hero Section */}
        <div className="container mx-auto">
          <div className="p-4">
            <div
              className="flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat sm:gap-8 sm:rounded-lg items-center justify-center p-4"
              style={{
                backgroundImage: 'linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2832&q=80")'
              }}
            >
              <div className="flex flex-col gap-2 text-center">
                <h1 className="text-white text-4xl font-black leading-tight tracking-[-0.033em] sm:text-5xl sm:font-black sm:leading-tight sm:tracking-[-0.033em]">
                  Welcome to Sudanese Association
                </h1>
                <h2 className="text-white text-sm font-normal leading-normal sm:text-base sm:font-normal sm:leading-normal">
                  Connecting our community, preserving our culture, and building a brighter future together. Join us in celebrating our heritage and supporting one another.
                </h2>
              </div>
              {user ? (
                <div className="text-center">
                  <p className="text-white text-lg mb-4">Welcome back, <strong>{user.email}</strong>!</p>
                  <Link
                    to="/events"
                    className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 sm:h-12 sm:px-5 bg-[#0c7ff2] text-white text-sm font-bold leading-normal tracking-[0.015em] sm:text-base sm:font-bold sm:leading-normal sm:tracking-[0.015em] hover:bg-[#0a6fd1]"
                  >
                    <span className="truncate">Explore Events</span>
                  </Link>
                </div>
              ) : (
                <Link
                  to="/login"
                  className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 sm:h-12 sm:px-5 bg-[#0c7ff2] text-white text-sm font-bold leading-normal tracking-[0.015em] sm:text-base sm:font-bold sm:leading-normal sm:tracking-[0.015em] hover:bg-[#0a6fd1]"
                >
                  <span className="truncate">Join Our Community</span>
                </Link>
              )}
            </div>
          </div>
        </div>

        {/* Upcoming Events Section */}
        <h2 className="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Upcoming Events</h2>
        <div className="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
          <div className="flex items-stretch p-4 gap-3">
            {latestEvents.length > 0 ? (
              latestEvents.map((event) => (
                <div key={event.id} className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                  <div
                    className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex flex-col"
                    style={{
                      backgroundImage: event.image_url
                        ? `url("${event.image_url}")`
                        : 'url("https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80")'
                    }}
                  />
                  <div>
                    <p className="text-[#111418] text-base font-medium leading-normal">{event.title}</p>
                    <p className="text-[#60758a] text-sm font-normal leading-normal">
                      {event.description?.substring(0, 60)}...
                    </p>
                    <p className="text-[#60758a] text-xs font-normal leading-normal mt-1">
                      {new Date(event.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                <div className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex flex-col bg-gray-200 items-center justify-center">
                  <p className="text-gray-500">No upcoming events</p>
                </div>
                <div>
                  <p className="text-[#111418] text-base font-medium leading-normal">Stay Tuned</p>
                  <p className="text-[#60758a] text-sm font-normal leading-normal">Check back soon for exciting events!</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Links Section */}
        <h2 className="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Quick Links</h2>
        <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
          <Link to="/members" className="flex flex-1 gap-3 rounded-lg border border-[#dbe0e6] bg-white p-4 items-center hover:bg-[#f8f9fa]">
            <div className="text-[#111418]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z" />
              </svg>
            </div>
            <h2 className="text-[#111418] text-base font-bold leading-tight">Meet Your Community</h2>
          </Link>
          <Link to="/events" className="flex flex-1 gap-3 rounded-lg border border-[#dbe0e6] bg-white p-4 items-center hover:bg-[#f8f9fa]">
            <div className="text-[#111418]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M208,32H184V24a8,8,0,0,0-16,0v8H88V24a8,8,0,0,0-16,0v8H48A16,16,0,0,0,32,48V208a16,16,0,0,0,16,16H208a16,16,0,0,0,16-16V48A16,16,0,0,0,208,32ZM72,48v8a8,8,0,0,0,16,0V48h80v8a8,8,0,0,0,16,0V48h24V80H48V48ZM208,208H48V96H208V208Zm-96-88v64a8,8,0,0,1-16,0V132.94l-4.42,2.22a8,8,0,0,1-7.16-14.32l16-8A8,8,0,0,1,112,120Zm59.16,30.45L152,176h16a8,8,0,0,1,0,16H136a8,8,0,0,1-6.4-12.8l28.78-38.37A8,8,0,1,0,145.07,132a8,8,0,1,1-13.85-8A24,24,0,0,1,176,136,23.76,23.76,0,0,1,171.16,150.45Z" />
              </svg>
            </div>
            <h2 className="text-[#111418] text-base font-bold leading-tight">View Event Calendar</h2>
          </Link>
          <Link to="/news" className="flex flex-1 gap-3 rounded-lg border border-[#dbe0e6] bg-white p-4 items-center hover:bg-[#f8f9fa]">
            <div className="text-[#111418]">
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path d="M88,112a8,8,0,0,1,8-8h80a8,8,0,0,1,0,16H96A8,8,0,0,1,88,112Zm8,40h80a8,8,0,0,0,0-16H96a8,8,0,0,0,0,16ZM232,64V184a24,24,0,0,1-24,24H32A24,24,0,0,1,8,184.11V88a8,8,0,0,1,16,0v96a8,8,0,0,0,16,0V64A16,16,0,0,1,56,48H216A16,16,0,0,1,232,64Zm-16,0H56V184a23.84,23.84,0,0,1-1.37,8H208a8,8,0,0,0,8-8Z" />
              </svg>
            </div>
            <h2 className="text-[#111418] text-base font-bold leading-tight">Latest News</h2>
          </Link>
        </div>

        {/* News Highlights Section */}
        <h2 className="text-[#111418] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">News Highlights</h2>
        {latestNews.length > 0 ? (
          latestNews.slice(0, 2).map((article) => (
            <div key={article.id} className="p-4">
              <div className="flex items-stretch justify-between gap-4 rounded-lg">
                <div className="flex flex-col gap-1 flex-[2_2_0px]">
                  <p className="text-[#111418] text-base font-bold leading-tight">{article.title}</p>
                  <p className="text-[#60758a] text-sm font-normal leading-normal">
                    {article.content?.substring(0, 100)}...
                  </p>
                  <p className="text-[#60758a] text-xs font-normal leading-normal mt-1">
                    {new Date(article.created_at).toLocaleDateString()}
                  </p>
                </div>
                <div
                  className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex-1"
                  style={{
                    backgroundImage: article.image_url
                      ? `url("${article.image_url}")`
                      : 'url("https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80")'
                  }}
                />
              </div>
            </div>
          ))
        ) : (
          <div className="p-4">
            <div className="flex items-stretch justify-between gap-4 rounded-lg">
              <div className="flex flex-col gap-1 flex-[2_2_0px]">
                <p className="text-[#111418] text-base font-bold leading-tight">Stay Connected</p>
                <p className="text-[#60758a] text-sm font-normal leading-normal">
                  Check back soon for the latest news and updates from our community.
                </p>
              </div>
              <div className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-lg flex-1 bg-gray-200 flex items-center justify-center">
                <p className="text-gray-500">No recent news</p>
              </div>
            </div>
          </div>
        )}

        {/* Call to Action */}
        <div className="flex justify-center">
          <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 max-w-[480px] justify-center">
            <Link
              to="/login"
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#0c7ff2] text-white text-sm font-bold leading-normal tracking-[0.015em] grow hover:bg-[#0a6fd1]"
            >
              <span className="truncate">Join Us</span>
            </Link>
            <Link
              to="/contact"
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#f0f2f5] text-[#111418] text-sm font-bold leading-normal tracking-[0.015em] grow hover:bg-[#e8eaed]"
            >
              <span className="truncate">Contact Us</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;