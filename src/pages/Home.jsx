import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../api/supabase';
import EventCard from '../components/EventCard';
import NewsArticleCard from '../components/NewsArticleCard';

const Home = () => {
  const { user } = useAuth();
  const [latestEvents, setLatestEvents] = useState([]);
  const [latestNews, setLatestNews] = useState([]);

  useEffect(() => {
    const fetchHomepageData = async () => {
      // Fetch latest 3 upcoming events
      const { data: eventsData } = await supabase
        .from('events')
        .select('*')
        .gte('date', new Date().toISOString())
        .order('date', { ascending: true })
        .limit(3);
      setLatestEvents(eventsData || []);

      // Fetch latest 3 news articles
      const { data: newsData } = await supabase.from('news').select('*').order('created_at', { ascending: false }).limit(3);
      setLatestNews(newsData || []);
    };

    fetchHomepageData();
  }, []);

  return (
    <div className="page-container">
      <section className="hero-section">
        <div className="hero-content">
          <h1>Welcome to the Sudanese Association</h1>
          <p>Connecting our community, preserving our culture, and building a brighter future together.</p>
          {user ? (
             <p>Welcome back, <strong>{user.email}</strong>!</p>
          ) : (
            <Link to="/login" className="cta-button">Join or Login</Link>
          )}
        </div>
      </section>

      <section className="home-section">
        <h2>Upcoming Events</h2>
        {latestEvents.length > 0 ? (
          <div className="highlights-grid">
            {latestEvents.map(event => <EventCard key={event.id} event={event} user={user} />)}
          </div>
        ) : (
          <p>No upcoming events. Check back soon!</p>
        )}
        <Link to="/events" className="see-all-link">See All Events</Link>
      </section>

      <section className="home-section">
        <h2>News Highlights</h2>
        {latestNews.length > 0 ? (
          <div className="highlights-grid news-highlights">
            {latestNews.map(article => <NewsArticleCard key={article.id} article={article} />)}
          </div>
        ) : (
          <p>No recent news.</p>
        )}
        <Link to="/news" className="see-all-link">Read All News</Link>
      </section>
    </div>
  );
};

export default Home;