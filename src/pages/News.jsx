import React, { useState, useEffect } from 'react';
import { supabase } from '../api/supabase';
import NewsArticleCard from '../components/NewsArticleCard';

const News = () => {
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchArticles = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from('news') // Assumes your table is named 'news'
          .select('*')
          .order('created_at', { ascending: false });

        if (error) throw error;
        setArticles(data);
      } catch (error) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchArticles();
  }, []);

  if (loading) return <div className="page-container">Loading news...</div>;
  if (error) return <div className="page-container error-message">Error: {error}</div>;

  return (
    <div className="page-container">
      <h1>News & Announcements</h1>
      {articles.length > 0 ? (
        <div className="news-list">
          {articles.map((article) => (
            <NewsArticleCard key={article.id} article={article} />
          ))}
        </div>
      ) : (
        <p>No news articles found.</p>
      )}
    </div>
  );
};

export default News;