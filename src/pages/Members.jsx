import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../api/supabase';

const Members = () => {
  const { user } = useAuth();
  const [announcements, setAnnouncements] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        const { data, error } = await supabase
          .from('member_announcements')
          .select('*')
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        setAnnouncements(data);
      } catch (error) {
        console.error('Error fetching member announcements:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnnouncements();
  }, []);

  return (
    <div className="page-container">
      <h1>Members Area</h1>
      <p>Welcome, <strong>{user?.email}</strong>. This is a protected area for members only.</p>
      
      <div className="member-content">
        <h2>Exclusive Announcements</h2>
        {loading ? (
          <p>Loading announcements...</p>
        ) : announcements.length > 0 ? (
          <ul className="announcements-list">
            {announcements.map(announcement => (
              <li key={announcement.id} className="announcement-item">
                <h3>{announcement.title}</h3>
                <p>{announcement.content}</p>
                <small>Posted on: {new Date(announcement.created_at).toLocaleDateString()}</small>
              </li>
            ))}
          </ul>
        ) : (
          <p>No announcements at this time.</p>
        )}
      </div>
    </div>
  );
};

export default Members;