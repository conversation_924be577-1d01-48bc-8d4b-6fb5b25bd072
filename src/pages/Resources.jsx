import React, { useState, useEffect } from 'react';
import { supabase } from '../api/supabase';
import { FaFilePdf, FaFileWord, FaFileAlt } from 'react-icons/fa';

const Resources = () => {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchFiles = async () => {
      const bucketName = 'resources';
      const folderName = 'public';

      try {
        setLoading(true);
        const { data, error: listError } = await supabase.storage
          .from(bucketName)
          .list(folderName);

        if (listError) throw listError;

        const fileData = data.filter(file => file.name !== '.emptyFolderPlaceholder');
        setFiles(fileData);
      } catch (err) {
        setError('Could not fetch resources. Please ensure the bucket is public and files exist.');
        console.error('Resources fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFiles();
  }, []);

  const handleDownload = async (fileName) => {
    try {
      const { data, error } = await supabase.storage
        .from('resources')
        .download(`public/${fileName}`);
      if (error) throw error;

      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
    } catch (err) {
      alert('Error downloading file: ' + err.message);
    }
  };

  const getFileIcon = (fileName) => {
    if (fileName.endsWith('.pdf')) return <FaFilePdf size={24} />;
    if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) return <FaFileWord size={24} />;
    return <FaFileAlt size={24} />;
  };

  if (loading) return <div className="page-container">Loading resources...</div>;
  if (error) return <div className="page-container error-message">{error}</div>;

  return (
    <div className="page-container">
      <h1>Resources</h1>
      <p>Downloadable documents, notes, and other helpful materials.</p>
      {files.length > 0 ? (
        <ul className="resources-list">
          {files.map(file => (
            <li key={file.id} className="resource-item">
              <div className="resource-info">
                {getFileIcon(file.name)}
                <span>{file.name}</span>
              </div>
              <button onClick={() => handleDownload(file.name)} className="download-btn">
                Download
              </button>
            </li>
          ))}
        </ul>
      ) : (
        <p>No resources are available at this time.</p>
      )}
    </div>
  );
};

export default Resources;