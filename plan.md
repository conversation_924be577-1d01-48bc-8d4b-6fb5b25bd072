# Association Website Project Plan

This document outlines the development plan for the association's web application.

---

## Phase 1: Project Setup & Core Foundation (Complete)
- [x] Initialize React Project using Vite
- [x] Install Core Dependencies (`@supabase/supabase-js`, `react-router-dom`)
- [x] Create and Configure a Supabase Project
- [x] Implement Supabase Configuration (`src/api/supabase.js`)
- [x] Establish a Scalable Project Structure (folders for pages, components, contexts, etc.)
- [x] Set up Client-Side Routing (`src/App.jsx`)
- [x] Create Global Authentication Context (`src/contexts/AuthContext.jsx`)

---

## Phase 2: Authentication & Basic Layout

- [x] **Authentication System (`src/pages/Login.jsx`)**
  - [x] Create Login/Register form UI
  - [x] Implement Email & Password Registration (`supabase.auth.signUp`)
  - [x] Implement Email & Password Sign-in (`supabase.auth.signInWithPassword`)
  - [x] Implement Google Sign-in (`supabase.auth.signInWithOAuth`)
  - [x] Implement Logout functionality (`supabase.auth.signOut`)
  - [x] Display user-friendly error messages
  - [x] Redirect user upon successful login/logout

- [x] **Layout Components (`src/components/layout/`)**
  - [x] Create `Navbar.jsx` component
    - [x] Display conditional links (e.g., "Login" vs. "Members Area" / "Logout") based on auth state
  - [x] Create `Footer.jsx` component
  - [x] Integrate `Navbar` and `Footer` into `App.jsx` to be visible on all pages

- [x] **Protected Routes**
  - [x] Create `ProtectedRoute.jsx` component
  - [x] Apply protection to the Members Area route in `App.jsx`

---

## Phase 3: Core Feature Implementation (Public Pages)

- [ ] **Homepage (`src/pages/Home.jsx`)**
  - [x] Build Hero section with welcome message and CTA buttons
  - [x] Create and implement an "Upcoming Events" slider (fetching data from Supabase)
  - [x] Add a "News Highlights" section

- [ ] **About Us Page (`src/pages/About.jsx`)**
  - [x] Add static content for History, Mission & Vision
  - [x] Create a component to display Executive Board members

- [ ] **Events Page (`src/pages/Events.jsx`)**
  - [x] Fetch and display a list of all upcoming events
  - [x] Add controls to filter events by category or date
  - [x] Implement an RSVP system (users can click a button to register their attendance, updating Supabase)
  - [x] Create an "Event Details" modal or card that shows more information

- [ ] **News/Blog Page (`src/pages/News.jsx`)**
  - [x] Fetch and display a list of all news articles
  - [x] Create a dynamic route and page to view a single article

- [ ] **Gallery Page (`src/pages/Gallery.jsx`)**
  - [x] Fetch and display photo albums from Supabase Storage
  - [x] Implement a lightbox view for larger images

- [ ] **Resources Page (`src/pages/Resources.jsx`)**
  - [x] List available documents (PDFs, notes) from Supabase Storage
  - [x] Implement file download functionality

- [ ] **Contact Page (`src/pages/Contact.jsx`)**
  - [x] Build the contact form (Name, Email, Subject, Message)
  - [x] Implement submission logic to save the message to Supabase

---

## Phase 4: Members & Admin Area (Protected)

- [x] **Members Area (`src/pages/Members.jsx`)**
  - [x] Fetch and display the logged-in user's profile information
  - [x] Display a list of exclusive announcements for members

- [x] **Admin Dashboard (Advanced)**
  - [x] Implement role-based access control (e.g., using custom claims or a roles table in Supabase)
  - [x] Create a protected route accessible only to admins
  - [x] **Content Management:**
    - [x] Create forms to Add/Edit/Delete Events
    - [x] Create forms to Add/Edit/Delete News Articles
  - [x] **Gallery Management:**
    - [x] Create an interface for admins to upload new photos

---

## Phase 5: Finalization & Deployment

- [ ] **Styling & Responsiveness:** Ensure the app is mobile-friendly and visually polished
- [ ] **Testing:** Thoroughly test all features, including authentication and form submissions
- [ ] **Deployment:** Deploy the final application to a hosting service (e.g., Vercel, Netlify)